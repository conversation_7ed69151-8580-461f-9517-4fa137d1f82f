@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* Loading optimization */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Smooth transitions */
* {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Critical CSS for above-the-fold content */
.critical-content {
    will-change: transform;
    transform: translateZ(0);
}
