import './bootstrap';

// Complete frontend functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 OnDaGames Frontend Loaded');

    // Initialize all components
    initializeLoadingScreen();
    initializeLazyLoading();
    initializeGameCards();
    initializeModals();
    initializeNavigation();
    initializeSearch();
    initializeNotifications();
    initializeTheme();
});

// Loading Screen Management
function initializeLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        // Show loading for minimum time for better UX
        setTimeout(() => {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
                loadingScreen.remove();
            }, 300);
        }, 800);
    }
}

// Lazy Loading for Images
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('loading-skeleton');
                    img.classList.add('fade-in');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => {
            img.classList.add('loading-skeleton');
            imageObserver.observe(img);
        });
    } else {
        // Fallback for older browsers
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

// Game Cards Functionality
function initializeGameCards() {
    const gameCards = document.querySelectorAll('.game-card');

    gameCards.forEach(card => {
        // Hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });

        // Click handling
        card.addEventListener('click', function() {
            const gameUrl = this.dataset.gameUrl;
            const gameName = this.dataset.gameName;

            if (gameUrl) {
                openGame(gameUrl, gameName);
            }
        });
    });
}

// Game Opening
function openGame(gameUrl, gameName) {
    console.log(`🎮 Opening game: ${gameName}`);

    // Create game modal or redirect
    if (gameUrl.includes('iframe') || gameUrl.includes('embed')) {
        openGameModal(gameUrl, gameName);
    } else {
        window.open(gameUrl, '_blank');
    }
}

// Modal System
function initializeModals() {
    // Close modal on background click
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });

    // Close modal on ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });
}

function openGameModal(gameUrl, gameName) {
    const modal = document.createElement('div');
    modal.className = 'modal fade-in';
    modal.innerHTML = `
        <div class="modal-content slide-up">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold">${gameName}</h3>
                <button class="close-modal text-2xl">&times;</button>
            </div>
            <div class="game-container">
                <iframe src="${gameUrl}" width="100%" height="400" frameborder="0"></iframe>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close button
    modal.querySelector('.close-modal').addEventListener('click', () => {
        closeModal(modal);
    });
}

function closeModal(modal) {
    modal.classList.add('fade-out');
    setTimeout(() => {
        modal.remove();
    }, 300);
}

// Navigation
function initializeNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Search Functionality
function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.querySelector('.search-results');

    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            } else {
                hideSearchResults();
            }
        });
    }
}

function performSearch(query) {
    console.log(`🔍 Searching for: ${query}`);

    // Show loading state
    showSearchLoading();

    // Perform search (replace with actual API call)
    fetch(`/api/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results);
        })
        .catch(error => {
            console.error('Search error:', error);
            hideSearchResults();
        });
}

function showSearchLoading() {
    const searchResults = document.querySelector('.search-results');
    if (searchResults) {
        searchResults.innerHTML = '<div class="loading-skeleton p-4">Searching...</div>';
        searchResults.style.display = 'block';
    }
}

function displaySearchResults(results) {
    const searchResults = document.querySelector('.search-results');
    if (searchResults && results) {
        if (results.length > 0) {
            searchResults.innerHTML = results.map(result => `
                <div class="search-result-item p-2 hover:bg-gray-100 cursor-pointer" data-game-url="${result.url}">
                    <img src="${result.image}" alt="${result.name}" class="w-12 h-12 inline-block mr-2">
                    <span>${result.name}</span>
                </div>
            `).join('');
        } else {
            searchResults.innerHTML = '<div class="p-4 text-gray-500">No games found</div>';
        }
        searchResults.style.display = 'block';
    }
}

function hideSearchResults() {
    const searchResults = document.querySelector('.search-results');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

// Notifications
function initializeNotifications() {
    // Auto-hide notifications after 5 seconds
    document.querySelectorAll('.notification').forEach(notification => {
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    });
}

// Theme Management
function initializeTheme() {
    const themeToggle = document.querySelector('.theme-toggle');
    const currentTheme = localStorage.getItem('theme') || 'dark';

    // Apply saved theme
    document.body.setAttribute('color-theme', currentTheme);

    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            const newTheme = document.body.getAttribute('color-theme') === 'dark' ? 'light' : 'dark';
            document.body.setAttribute('color-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });
    }
}

// Utility Functions
window.showNotification = function(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 fade-in`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
};

window.openGame = openGame;
window.closeModal = closeModal;

// Performance monitoring
if (window.performance) {
    window.addEventListener('load', function() {
        const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
        console.log(`⚡ Page loaded in ${loadTime}ms`);

        if (loadTime > 3000) {
            console.warn('⚠️ Slow page load detected');
        }
    });
}
