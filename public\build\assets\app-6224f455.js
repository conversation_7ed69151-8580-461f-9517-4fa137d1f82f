import{a as e}from"./vendor-fb5f9e0e.js";window.axios=e,window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";let t=document.head.querySelector('meta[name="csrf-token"]');t&&(window.axios.defaults.headers.common["X-CSRF-TOKEN"]=t.content),document.addEventListener("DOMContentLoaded",(function(){const e=document.getElementById("loading-screen");e&&setTimeout((()=>{e.style.display="none"}),100)}));
