import{a as e}from"./vendor-fb5f9e0e.js";window.axios=e,window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";let t=document.head.querySelector('meta[name="csrf-token"]');function n(e,t){e.includes("iframe")||e.includes("embed")?function(e,t){const n=document.createElement("div");n.className="modal fade-in",n.innerHTML=`\n        <div class="modal-content slide-up">\n            <div class="flex justify-between items-center mb-4">\n                <h3 class="text-xl font-bold">${t}</h3>\n                <button class="close-modal text-2xl">&times;</button>\n            </div>\n            <div class="game-container">\n                <iframe src="${e}" width="100%" height="400" frameborder="0"></iframe>\n            </div>\n        </div>\n    `,document.body.appendChild(n),n.querySelector(".close-modal").addEventListener("click",(()=>{o(n)}))}(e,t):window.open(e,"_blank")}function o(e){e.classList.add("fade-out"),setTimeout((()=>{e.remove()}),300)}function s(){const e=document.querySelector(".search-results");e&&(e.style.display="none")}t&&(window.axios.defaults.headers.common["X-CSRF-TOKEN"]=t.content),document.addEventListener("DOMContentLoaded",(function(){!function(){const e=document.getElementById("loading-screen");e&&setTimeout((()=>{e.classList.add("fade-out"),setTimeout((()=>{e.remove()}),300)}),800)}(),function(){const e=document.querySelectorAll("img[data-src]");if("IntersectionObserver"in window){const t=new IntersectionObserver(((e,t)=>{e.forEach((e=>{if(e.isIntersecting){const n=e.target;n.src=n.dataset.src,n.classList.remove("loading-skeleton"),n.classList.add("fade-in"),t.unobserve(n)}}))}));e.forEach((e=>{e.classList.add("loading-skeleton"),t.observe(e)}))}else e.forEach((e=>{e.src=e.dataset.src}))}(),document.querySelectorAll(".game-card").forEach((e=>{e.addEventListener("mouseenter",(function(){this.style.transform="translateY(-4px) scale(1.02)"})),e.addEventListener("mouseleave",(function(){this.style.transform="translateY(0) scale(1)"})),e.addEventListener("click",(function(){const e=this.dataset.gameUrl,t=this.dataset.gameName;e&&n(e,t)}))})),document.addEventListener("click",(function(e){e.target.classList.contains("modal")&&o(e.target)})),document.addEventListener("keydown",(function(e){if("Escape"===e.key){const e=document.querySelector(".modal");e&&o(e)}})),function(){const e=document.querySelector(".nav-toggle"),t=document.querySelector(".nav-menu");e&&t&&e.addEventListener("click",(function(){t.classList.toggle("active")}));document.querySelectorAll('a[href^="#"]').forEach((e=>{e.addEventListener("click",(function(e){e.preventDefault();const t=document.querySelector(this.getAttribute("href"));t&&t.scrollIntoView({behavior:"smooth",block:"start"})}))}))}(),function(){const e=document.querySelector(".search-input");if(document.querySelector(".search-results"),e){let t;e.addEventListener("input",(function(){clearTimeout(t);const e=this.value.trim();e.length>=2?t=setTimeout((()=>{!function(e){(function(){const e=document.querySelector(".search-results");e&&(e.innerHTML='<div class="loading-skeleton p-4">Searching...</div>',e.style.display="block")})(),fetch(`/api/search?q=${encodeURIComponent(e)}`).then((e=>e.json())).then((e=>{!function(e){const t=document.querySelector(".search-results");t&&e&&(e.length>0?t.innerHTML=e.map((e=>`\n                <div class="search-result-item p-2 hover:bg-gray-100 cursor-pointer" data-game-url="${e.url}">\n                    <img src="${e.image}" alt="${e.name}" class="w-12 h-12 inline-block mr-2">\n                    <span>${e.name}</span>\n                </div>\n            `)).join(""):t.innerHTML='<div class="p-4 text-gray-500">No games found</div>',t.style.display="block")}(e.results)})).catch((e=>{s()}))}(e)}),300):s()}))}}(),document.querySelectorAll(".notification").forEach((e=>{setTimeout((()=>{e.classList.add("fade-out"),setTimeout((()=>{e.remove()}),300)}),5e3)})),function(){const e=document.querySelector(".theme-toggle"),t=localStorage.getItem("theme")||"dark";document.body.setAttribute("color-theme",t),e&&e.addEventListener("click",(function(){const e="dark"===document.body.getAttribute("color-theme")?"light":"dark";document.body.setAttribute("color-theme",e),localStorage.setItem("theme",e)}))}()})),window.showNotification=function(e,t="info"){const n=document.createElement("div");n.className=`notification notification-${t} fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 fade-in`,n.textContent=e,document.body.appendChild(n),setTimeout((()=>{n.classList.add("fade-out"),setTimeout((()=>{n.remove()}),300)}),3e3)},window.openGame=n,window.closeModal=o,window.performance&&window.addEventListener("load",(function(){window.performance.timing.loadEventEnd,window.performance.timing.navigationStart}));
