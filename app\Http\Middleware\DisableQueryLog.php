<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DisableQueryLog
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Desabilitar query log para máxima performance
        DB::connection()->disableQueryLog();
        
        return $next($request);
    }
}
