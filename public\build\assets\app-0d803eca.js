import{a as e}from"./vendor-fb5f9e0e.js";window.axios=e,window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";let t=document.head.querySelector('meta[name="csrf-token"]');t&&(window.axios.defaults.headers.common["X-CSRF-TOKEN"]=t.content),window.performance&&window.performance.mark&&(window.performance.mark("app-start"),window.addEventListener("load",(function(){window.performance.mark("app-loaded"),window.performance.measure("app-load-time","app-start","app-loaded");const e=window.performance.getEntriesByName("app-load-time")[0];e&&e.duration}))),document.addEventListener("DOMContentLoaded",(function(){const e=document.querySelectorAll("img[data-src]"),t=new IntersectionObserver(((e,o)=>{e.forEach((e=>{if(e.isIntersecting){const o=e.target;o.src=o.dataset.src,o.removeAttribute("data-src"),t.unobserve(o)}}))}));e.forEach((e=>t.observe(e)));["/assets/css/fontawesome.min.css","/css/filament.css"].forEach((e=>{const t=document.createElement("link");t.rel="preload",t.as="style",t.href=e,document.head.appendChild(t)})),window.addEventListener("load",(function(){const e=document.getElementById("loading-screen");e&&setTimeout((()=>{e.style.opacity="0",setTimeout((()=>{e.remove()}),300)}),500)}))}));
