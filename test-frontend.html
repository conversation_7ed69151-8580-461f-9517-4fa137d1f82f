<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnDaGames - Frontend Test</title>
    <link href="/build/assets/app-98161035.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gray-900 flex items-center justify-center z-50">
        <div class="text-center">
            <div class="loading-spinner mb-4"></div>
            <h2 class="text-white text-xl font-bold">OnDaGames</h2>
            <p class="text-gray-400">Carregando...</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto p-4">
        <header class="nav nav-dark mb-8">
            <h1 class="text-2xl font-bold">🎮 OnDaGames</h1>
            <div class="flex items-center space-x-4">
                <input type="text" class="search-input px-4 py-2 rounded bg-gray-800 text-white" placeholder="Buscar jogos...">
                <button class="theme-toggle btn btn-primary">🌙</button>
            </div>
        </header>

        <!-- Search Results -->
        <div class="search-results hidden bg-gray-800 rounded-lg shadow-lg"></div>

        <!-- Game Grid -->
        <div class="game-grid">
            <div class="game-card card card-dark" data-game-url="https://example.com/game1" data-game-name="Jogo 1">
                <img src="https://via.placeholder.com/200x150/3b82f6/ffffff?text=Jogo+1" alt="Jogo 1" class="game-image">
                <div class="p-4">
                    <h3 class="font-bold">Jogo de Teste 1</h3>
                    <p class="text-gray-400">Descrição do jogo</p>
                </div>
            </div>

            <div class="game-card card card-dark" data-game-url="https://example.com/game2" data-game-name="Jogo 2">
                <img src="https://via.placeholder.com/200x150/10b981/ffffff?text=Jogo+2" alt="Jogo 2" class="game-image">
                <div class="p-4">
                    <h3 class="font-bold">Jogo de Teste 2</h3>
                    <p class="text-gray-400">Descrição do jogo</p>
                </div>
            </div>

            <div class="game-card card card-dark" data-game-url="https://example.com/game3" data-game-name="Jogo 3">
                <img src="https://via.placeholder.com/200x150/f59e0b/ffffff?text=Jogo+3" alt="Jogo 3" class="game-image">
                <div class="p-4">
                    <h3 class="font-bold">Jogo de Teste 3</h3>
                    <p class="text-gray-400">Descrição do jogo</p>
                </div>
            </div>

            <div class="game-card card card-dark" data-game-url="https://example.com/game4" data-game-name="Jogo 4">
                <img src="https://via.placeholder.com/200x150/ef4444/ffffff?text=Jogo+4" alt="Jogo 4" class="game-image">
                <div class="p-4">
                    <h3 class="font-bold">Jogo de Teste 4</h3>
                    <p class="text-gray-400">Descrição do jogo</p>
                </div>
            </div>
        </div>

        <!-- Test Buttons -->
        <div class="mt-8 text-center space-x-4">
            <button onclick="showNotification('Teste de notificação!', 'success')" class="btn btn-primary">
                Testar Notificação
            </button>
            <button onclick="openGame('https://example.com/test-game', 'Jogo de Teste')" class="btn btn-primary">
                Abrir Jogo Modal
            </button>
        </div>
    </div>

    <script src="/build/assets/vendor-fb5f9e0e.js"></script>
    <script src="/build/assets/app-9a7f65fc.js"></script>

    <style>
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #374151;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .fade-out {
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }

        .notification {
            position: fixed;
            top: 1rem;
            right: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .notification-success {
            background-color: #10b981;
            color: white;
        }

        .notification-error {
            background-color: #ef4444;
            color: white;
        }

        .notification-info {
            background-color: #3b82f6;
            color: white;
        }
    </style>
</body>
</html>
