import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// CSRF Token
let token = document.head.querySelector('meta[name="csrf-token"]');

if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Performance monitoring
if (window.performance && window.performance.mark) {
    window.performance.mark('app-start');
    
    window.addEventListener('load', function() {
        window.performance.mark('app-loaded');
        window.performance.measure('app-load-time', 'app-start', 'app-loaded');
        
        const measure = window.performance.getEntriesByName('app-load-time')[0];
        if (measure && measure.duration > 3000) {
            console.warn('Slow page load detected:', measure.duration + 'ms');
        }
    });
}
